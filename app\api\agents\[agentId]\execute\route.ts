import { NextResponse } from "next/server";
import { nanoid } from "nanoid";
import type { Agent, AgentExecution } from "@/lib/types";

const globalForAgents = globalThis as unknown as {
  agents?: Agent[];
};

globalForAgents.agents = globalForAgents.agents ?? [];

interface Params {
  params: { agentId: string };
}

export async function POST(request: Request, { params }: Params) {
  const agent = globalForAgents.agents!.find((a) => a.id === params.agentId);
  if (!agent) {
    return new NextResponse("Agent not found", { status: 404 });
  }

  const { input } = await request.json();

  // Very naive execution mock
  const execution: AgentExecution = {
    id: nanoid(),
    agentId: agent.id,
    userId: "demo",
    status: "COMPLETED",
    input,
    output: { message: "Execution successful" },
    executionPath: agent.steps?.map((s) => s.id) ?? [],
    startTime: new Date().toISOString(),
    endTime: new Date().toISOString(),
    tokenUsage: 0,
    stepResults: [],
  };

  return NextResponse.json(execution, { status: 201 });
}
