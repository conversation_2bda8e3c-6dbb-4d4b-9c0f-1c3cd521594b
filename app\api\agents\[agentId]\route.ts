import { NextResponse } from "next/server";
import type { Agent } from "@/lib/types";

const globalForAgents = globalThis as unknown as {
  agents?: Agent[];
};

globalForAgents.agents = globalForAgents.agents ?? [];

interface Params {
  params: { agentId: string };
}

export async function GET(request: Request, { params }: Params) {
  const agent = globalForAgents.agents!.find((a) => a.id === params.agentId);
  if (!agent) {
    return new NextResponse("Agent not found", { status: 404 });
  }
  return NextResponse.json(agent);
}

export async function PATCH(request: Request, { params }: Params) {
  const agent = globalForAgents.agents!.find((a) => a.id === params.agentId);
  if (!agent) {
    return new NextResponse("Agent not found", { status: 404 });
  }
  const data = await request.json();
  if (data.name !== undefined) agent.name = data.name;
  if (data.description !== undefined) agent.description = data.description;
  agent.updatedAt = new Date().toISOString();
  return NextResponse.json(agent);
}

export async function DELETE(request: Request, { params }: Params) {
  const idx = globalForAgents.agents!.findIndex((a) => a.id === params.agentId);
  if (idx === -1) {
    return new NextResponse("Agent not found", { status: 404 });
  }
  globalForAgents.agents!.splice(idx, 1);
  return new NextResponse(null, { status: 204 });
}
