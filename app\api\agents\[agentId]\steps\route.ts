import { NextResponse } from "next/server";
import { nanoid } from "nanoid";
import type { Agent, AgentStep } from "@/lib/types";

const globalForAgents = globalThis as unknown as {
  agents?: Agent[];
};

globalForAgents.agents = globalForAgents.agents ?? [];

interface Params {
  params: { agentId: string };
}

export async function POST(request: Request, { params }: Params) {
  const agent = globalForAgents.agents!.find((a) => a.id === params.agentId);
  if (!agent) {
    return new NextResponse("Agent not found", { status: 404 });
  }

  const data = await request.json();
  const newStep: AgentStep = {
    id: nanoid(),
    agentId: agent.id,
    name: data.name,
    description: data.description,
    type: data.type || "ACTION",
    config: data.config ?? {},
    order: data.order ?? (agent.steps?.length ?? 0) + 1,
    nextOnSuccess: data.nextOnSuccess,
    nextOnFailure: data.nextOnFailure,
  };

  agent.steps = agent.steps ?? [];
  agent.steps.push(newStep);
  agent.updatedAt = new Date().toISOString();

  return NextResponse.json(newStep, { status: 201 });
}
