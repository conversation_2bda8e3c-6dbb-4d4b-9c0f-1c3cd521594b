import { NextResponse } from "next/server";
import { nanoid } from "nanoid";
import type { Agent } from "@/lib/types";

// Use a global cache so that the data survives hot-reloads in dev
const globalForAgents = globalThis as unknown as {
  agents?: Agent[];
};

globalForAgents.agents = globalForAgents.agents ?? [];

export async function GET() {
  return NextResponse.json(globalForAgents.agents);
}

export async function POST(request: Request) {
  const { name, description } = await request.json();
  if (!name) {
    return new NextResponse("Name is required", { status: 400 });
  }

  const newAgent: Agent = {
    id: nanoid(),
    name,
    description,
    userId: "demo", // Replace with real auth integration
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    steps: [],
    variables: [],
    credentials: [],
  };

  globalForAgents.agents!.push(newAgent);

  return NextResponse.json(newAgent, { status: 201 });
}
