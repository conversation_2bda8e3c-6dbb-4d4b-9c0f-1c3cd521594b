import { NextResponse } from "next/server";
import { nanoid } from "nanoid";
import type { AIModel } from "@/lib/types";

// Static catalogue of models grouped by provider. Expand as you add keys.
// NOTE: The `model` field is the identifier you pass to the AI SDK.
const catalog: AIModel[] = [
  // OpenAI
  {
    id: nanoid(),
    name: "GPT-4o",
    description: "OpenAI GPT-4o (vision + text)",
    model: "gpt-4o-mini",
    type: "openai",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "GPT-4 Turbo",
    description: "OpenAI GPT-4 Turbo 128k",
    model: "gpt-4o",
    type: "openai",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "GPT-3.5 Turbo",
    description: "OpenAI GPT-3.5 Turbo",
    model: "gpt-3.5-turbo",
    type: "openai",
    createdAt: new Date().toISOString(),
  },
  // Google Gemini
  {
    id: nanoid(),
    name: "Gemini Flash 2.5",
    description: "Google Gemini Flash 2.5 (vision, fast)",
    model: "gemini-flash-2.5",
    type: "google",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Gemini Pro 1.5",
    description: "Google Gemini 1.5 Pro (128k)",
    model: "gemini-pro-1.5",
    type: "google",
    createdAt: new Date().toISOString(),
  },
  // Anthropic
  {
    id: nanoid(),
    name: "Claude 3.5 Sonnet",
    description: "Anthropic Claude 3.5 Sonnet",
    model: "claude-3.5-sonnet-20240620",
    type: "anthropic",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Claude 3 Opus",
    description: "Anthropic Claude 3 Opus",
    model: "claude-3-opus-20240229",
    type: "anthropic",
    createdAt: new Date().toISOString(),
  },
  // Groq (Llama 3)
  {
    id: nanoid(),
    name: "Llama 3 70B",
    description: "Groq Llama 3 70B blazing-fast",
    model: "llama3-70b-8192",
    type: "groq",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Llama 3 8B",
    description: "Groq Llama 3 8B",
    model: "llama3-8b-8192",
    type: "groq",
    createdAt: new Date().toISOString(),
  },
  // Mistral via Fireworks
  {
    id: nanoid(),
    name: "Mixtral 8x22B",
    description: "Fireworks Mixtral 8x22B",
    model: "accounts/fireworks/models/mixtral-8x22b-instruct",
    type: "fireworks",
    createdAt: new Date().toISOString(),
  },
  // OpenRouter community models example
  {
    id: nanoid(),
    name: "Llama 4 Maverick",
    description: "OpenRouter Llama 4 Maverick",
    model: "meta-llama/llama-4-maverick-200b",
    type: "openrouter",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "GPT-4o Mini (2024-07-18)",
    description: "OpenAI GPT-4o-mini 2024-07-18",
    model: "gpt-4o-mini-2024-07-18",
    type: "openai",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "ChatGPT-4o (latest)",
    description: "OpenAI GPT-4o latest",
    model: "chatgpt-4o-latest",
    type: "openai",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "o3-mini",
    description: "OpenAI o3 mini",
    model: "o3-mini",
    type: "openai",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "o4-mini",
    description: "OpenAI o4 mini",
    model: "o4-mini",
    type: "openai",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "GPT-4.1",
    description: "OpenAI GPT-4.1",
    model: "gpt-4.1",
    type: "openai",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "GPT-4.1 Mini",
    description: "OpenAI GPT-4.1 Mini",
    model: "gpt-4.1-mini",
    type: "openai",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "GPT-4.1 Nano",
    description: "OpenAI GPT-4.1 Nano",
    model: "gpt-4.1-nano",
    type: "openai",
    createdAt: new Date().toISOString(),
  },
  // Gemini models
  {
    id: nanoid(),
    name: "Gemini Flash 2.0",
    description: "Gemini 2.0 Flash (fast, cheap)",
    model: "gemini-2.0-flash",
    type: "google",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Gemini Flash 2.0 Lite",
    description: "Gemini 2.0 Flash Lite",
    model: "gemini-2.0-flash-lite",
    type: "google",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Gemini Pro 2.5 (preview)",
    description: "Gemini 2.5 Pro preview 05-06",
    model: "gemini-2.5-pro-preview-05-06",
    type: "google",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Gemini Flash 2.5 (preview)",
    description: "Gemini 2.5 Flash preview 05-20",
    model: "gemini-2.5-flash-preview-05-20",
    type: "google",
    createdAt: new Date().toISOString(),
  },
  // OpenRouter large set (examples)
  {
    id: nanoid(),
    name: "Deepseek Chat V3 0324 (free)",
    description: "OpenRouter deepseek-chat-v3-0324",
    model: "deepseek/deepseek-chat-v3-0324:free",
    type: "openrouter",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Deepseek R1 (free)",
    description: "OpenRouter deepseek-r1",
    model: "deepseek/deepseek-r1:free",
    type: "openrouter",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Deepseek R1T Chimera (free)",
    description: "OpenRouter deepseek-r1t-chimera",
    model: "tngtech/deepseek-r1t-chimera:free",
    type: "openrouter",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Deepseek Prover V2 (free)",
    description: "OpenRouter deepseek-prover-v2",
    model: "deepseek/deepseek-prover-v2:free",
    type: "openrouter",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Devstral Small (free)",
    description: "OpenRouter mistralai/devstral-small",
    model: "mistralai/devstral-small:free",
    type: "openrouter",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Qwen2.5-VL-72B (free)",
    description: "OpenRouter qwen2.5 VL 72B",
    model: "qwen/qwen2.5-vl-72b-instruct:free",
    type: "openrouter",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Mistral Small 3.1 24B (free)",
    description: "OpenRouter mistral small 3.1 24B",
    model: "mistralai/mistral-small-3.1-24b-instruct:free",
    type: "openrouter",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Gemma-3-27B-IT (free)",
    description: "OpenRouter Gemma 3 27B IT",
    model: "google/gemma-3-27b-it:free",
    type: "openrouter",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Reka Flash 3 (free)",
    description: "OpenRouter Reka Flash 3",
    model: "rekaai/reka-flash-3:free",
    type: "openrouter",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Gemini 2.5 Pro (exp-03-25) free",
    description: "OpenRouter Gemini 2.5-Pro exp 03-25",
    model: "google/gemini-2.5-pro-exp-03-25:free",
    type: "openrouter",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Qwen3 Ultra (free)",
    description: "OpenRouter qwen3 Ultra 235B",
    model: "qwen/qwen3-235b-a22b:free",
    type: "openrouter",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Qwen3 Mini (free)",
    description: "OpenRouter qwen3 30B Mini",
    model: "qwen/qwen3-30b-a3b:free",
    type: "openrouter",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Qwen3 32B (free)",
    description: "OpenRouter qwen3 32B",
    model: "qwen/qwen3-32b:free",
    type: "openrouter",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Llama3 Ultra 253B (free)",
    description: "OpenRouter NVIDIA Llama 3.1 Ultra 253B",
    model: "nvidia/llama-3.1-nemotron-ultra-253b-v1:free",
    type: "openrouter",
    createdAt: new Date().toISOString(),
  },
  // Groq extras
  {
    id: nanoid(),
    name: "Deepseek R1 Groq",
    description: "Groq Deepseek R1 distill Llama 70B",
    model: "deepseek-r1-distill-llama-70b",
    type: "groq",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Deepseek R1 Groq Qwen 32B",
    description: "Groq Deepseek R1 distill Qwen 32B",
    model: "deepseek-r1-distill-qwen-32b",
    type: "groq",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Llama 3.3 70B Versatile",
    description: "Groq Llama 3.3 70B Versatile",
    model: "llama-3.3-70b-versatile",
    type: "groq",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Llama 3.2 90B Vision Preview",
    description: "Groq Llama 3.2 90B Vision Preview",
    model: "llama-3.2-90b-vision-preview",
    type: "groq",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Compound Beta",
    description: "Groq Compound Beta",
    model: "compound-beta",
    type: "groq",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Compound Beta Mini",
    description: "Groq Compound Beta Mini",
    model: "compound-beta-mini",
    type: "groq",
    createdAt: new Date().toISOString(),
  },
  // Anthropic new models
  {
    id: nanoid(),
    name: "Claude Sonnet 4 (2025-05-14)",
    description: "Anthropic Claude Sonnet 4 20250514",
    model: "claude-sonnet-4-20250514",
    type: "anthropic",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Claude Opus 4 (2025-05-14)",
    description: "Anthropic Claude Opus 4 20250514",
    model: "claude-opus-4-20250514",
    type: "anthropic",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Claude 3-7 Sonnet (latest)",
    description: "Anthropic Claude 3-7 Sonnet latest",
    model: "claude-3-7-sonnet-latest",
    type: "anthropic",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Claude 3-5 Sonnet (latest)",
    description: "Anthropic Claude 3.5 Sonnet latest",
    model: "claude-3-5-sonnet-latest",
    type: "anthropic",
    createdAt: new Date().toISOString(),
  },
  {
    id: nanoid(),
    name: "Claude 3-5 Haiku (latest)",
    description: "Anthropic Claude 3.5 Haiku latest",
    model: "claude-3-5-haiku-latest",
    type: "anthropic",
    createdAt: new Date().toISOString(),
  },
];

export async function GET() {
  return NextResponse.json(catalog);
}
