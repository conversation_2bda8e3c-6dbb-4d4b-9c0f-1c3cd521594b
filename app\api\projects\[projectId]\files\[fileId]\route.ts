import { NextResponse } from "next/server";
import type { ProjectFile } from "@/lib/types";

const globalForProjectFiles = globalThis as unknown as {
  projectFiles?: Record<string, ProjectFile[]>;
};

globalForProjectFiles.projectFiles = globalForProjectFiles.projectFiles ?? {};

interface Params {
  params: { projectId: string; fileId: string };
}

export async function GET(request: Request, { params }: Params) {
  const bucket = globalForProjectFiles.projectFiles![params.projectId] ?? [];
  const file = bucket.find((f) => f.id === params.fileId);
  if (!file) {
    return new NextResponse("File not found", { status: 404 });
  }
  return NextResponse.json(file);
}

export async function PATCH(request: Request, { params }: Params) {
  const bucket = globalForProjectFiles.projectFiles![params.projectId] ?? [];
  const file = bucket.find((f) => f.id === params.fileId);
  if (!file) {
    return new NextResponse("File not found", { status: 404 });
  }
  const { content, commitMsg } = await request.json();
  // Update file currentVersion and updatedAt
  file.currentVersion = {
    ...file.currentVersion,
    version: file.currentVersion.version + 1,
    content: content ?? file.currentVersion.content,
    commitMsg: commitMsg ?? "update",
    createdAt: new Date().toISOString(),
  };
  file.updatedAt = new Date().toISOString();
  return NextResponse.json(file);
}
