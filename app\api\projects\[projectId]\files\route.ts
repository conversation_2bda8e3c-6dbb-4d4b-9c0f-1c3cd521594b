import { NextResponse } from "next/server";
import { nanoid } from "nanoid";
import type { ProjectFile } from "@/lib/types";

const globalForProjectFiles = globalThis as unknown as {
  projectFiles?: Record<string, ProjectFile[]>;
};

globalForProjectFiles.projectFiles = globalForProjectFiles.projectFiles ?? {};

interface Params {
  params: { projectId: string };
}

export async function GET(request: Request, { params }: Params) {
  const files = globalForProjectFiles.projectFiles![params.projectId] ?? [];
  return NextResponse.json(files);
}

export async function POST(request: Request, { params }: Params) {
  const { name, path, content = "", commitMsg } = await request.json();
  if (!name || !path) {
    return new NextResponse("Name and path are required", { status: 400 });
  }

  const newFile: ProjectFile = {
    id: nanoid(),
    projectId: params.projectId,
    name,
    path,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    currentVersionId: nanoid(),
    isDeleted: false,
    currentVersion: {
      id: nanoid(),
      fileId: "",
      version: 1,
      content,
      commitMsg: commitMsg ?? "initial",
      authorId: "demo",
      createdAt: new Date().toISOString(),
      isDeleted: false,
    },
  };

  const bucket = (globalForProjectFiles.projectFiles![params.projectId] =
    globalForProjectFiles.projectFiles![params.projectId] ?? []);
  bucket.push(newFile);
  return NextResponse.json(newFile, { status: 201 });
}
