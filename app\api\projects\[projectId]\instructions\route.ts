import { NextResponse } from "next/server";

interface Params {
  params: { projectId: string };
}

export async function POST(request: Request, { params }: Params) {
  const { instruction, threadId } = await request.json();
  if (!instruction) {
    return new NextResponse("Instruction is required", { status: 400 });
  }

  // Mock implementation that simply echoes back a canned response
  const result = {
    projectId: params.projectId,
    threadId: threadId ?? null,
    instruction,
    result: `Processed instruction for project ${params.projectId}`,
  };

  return NextResponse.json(result, { status: 200 });
}
