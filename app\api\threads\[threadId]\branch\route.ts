import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { auth } from "@clerk/nextjs/server";
import { nanoid } from "nanoid";

const prisma = new PrismaClient();

interface Params {
  params: { threadId: string };
}

export async function POST(request: Request, { params }: Params) {
  try {
    const { userId } = await auth();
    const { threadId } = params;
    const { messageId } = await request.json();

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    if (!messageId) {
      return new NextResponse("messageId is required", { status: 400 });
    }

    // Fetch original thread and messages
    const original = await prisma.chatThread.findUnique({
      where: { id: threadId },
      include: {
        messages: { orderBy: { createdAt: "asc" } },
      },
    });

    if (!original || original.userId !== userId) {
      return new NextResponse("Not Found", { status: 404 });
    }

    // Filter messages up to and including messageId
    const messagesToCopy = [] as { role: string; content: string }[];
    for (const msg of original.messages) {
      messagesToCopy.push({ role: msg.role, content: msg.content });
      if (msg.id === messageId) break;
    }

    // Create new thread
    const newThread = await prisma.chatThread.create({
      data: {
        id: nanoid(),
        userId,
        title: `${original.title} (Branch)`,
        messages: {
          create: messagesToCopy,
        },
      },
      include: { messages: true },
    });

    return NextResponse.json(newThread, { status: 201 });
  } catch (error) {
    console.error("[THREAD_BRANCH]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
