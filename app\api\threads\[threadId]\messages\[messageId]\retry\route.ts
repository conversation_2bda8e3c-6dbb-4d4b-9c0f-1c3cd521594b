import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { auth } from "@clerk/nextjs/server";

const prisma = new PrismaClient();

interface Params {
  params: { threadId: string; messageId: string };
}

// POST /api/threads/[threadId]/messages/[messageId]/retry – delete messages after given message
export async function POST(request: Request, { params }: Params) {
  try {
    const { userId } = await auth();
    const { threadId, messageId } = params;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // verify thread ownership and get message create timestamp
    const message = await prisma.chatMessage.findUnique({
      where: { id: messageId },
      include: { thread: true },
    });

    if (
      !message ||
      message.threadId !== threadId ||
      message.thread.userId !== userId
    ) {
      return new NextResponse("Not Found", { status: 404 });
    }

    // Delete messages created after this message (id order not guarantee) using createdAt
    await prisma.chatMessage.deleteMany({
      where: {
        threadId,
        createdAt: { gt: message.createdAt },
      },
    });

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error("[THREAD_MESSAGE_RETRY]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
