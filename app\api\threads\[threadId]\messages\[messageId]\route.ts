import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { auth } from "@clerk/nextjs/server";

const prisma = new PrismaClient();

interface Params {
  params: { threadId: string; messageId: string };
}

// PATCH /api/threads/[threadId]/messages/[messageId] – update message content
export async function PATCH(request: Request, { params }: Params) {
  try {
    const { userId } = await auth();
    const { threadId, messageId } = params;
    const { content } = await request.json();

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    if (!content) {
      return new NextResponse("Content is required", { status: 400 });
    }

    // Ensure the message belongs to the user's thread
    const message = await prisma.chatMessage.findUnique({
      where: { id: messageId },
      include: { thread: true },
    });

    if (
      !message ||
      message.threadId !== threadId ||
      message.thread.userId !== userId
    ) {
      return new NextResponse("Not Found", { status: 404 });
    }

    const updated = await prisma.chatMessage.update({
      where: { id: messageId },
      data: { content },
    });

    return NextResponse.json(updated);
  } catch (error) {
    console.error("[THREAD_MESSAGE_PATCH]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
