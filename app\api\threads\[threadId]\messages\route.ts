import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { auth } from "@clerk/nextjs/server";

const prisma = new PrismaClient();

interface Params {
  params: { threadId: string };
}

// GET /api/threads/[threadId]/messages – list messages for the thread
export async function GET(request: Request, { params }: Params) {
  try {
    const { userId } = await auth();
    const { threadId } = params;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const thread = await prisma.chatThread.findUnique({
      where: { id: threadId },
      include: {
        messages: { orderBy: { createdAt: "asc" } },
      },
    });

    if (!thread || thread.userId !== userId) {
      return new NextResponse("Not Found", { status: 404 });
    }

    return NextResponse.json(thread.messages);
  } catch (error) {
    console.error("[THREAD_MESSAGES_GET]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}

// POST /api/threads/[threadId]/messages – append a new user message (optional helper)
export async function POST(request: Request, { params }: Params) {
  try {
    const { userId } = await auth();
    const { threadId } = params;
    const { role = "user", content } = await request.json();

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    if (!content) {
      return new NextResponse("Content is required", { status: 400 });
    }

    // Verify thread ownership
    const thread = await prisma.chatThread.findUnique({
      where: { id: threadId },
    });
    if (!thread || thread.userId !== userId) {
      return new NextResponse("Not Found", { status: 404 });
    }

    const message = await prisma.chatMessage.create({
      data: {
        threadId,
        role,
        content,
      },
    });

    return NextResponse.json(message, { status: 201 });
  } catch (error) {
    console.error("[THREAD_MESSAGES_POST]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
