@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

/* Theme transition for smooth color changes */
.theme-transition * {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

@layer base {
  /* Light mode - default theme */
  :root {
    /* Apple-inspired light mode color palette */
    --background: 210 30% 98%;
    --foreground: 220 25% 20%;
    --card: 0 0% 100%;
    --card-foreground: 220 25% 20%;
    --popover: 0 0% 100%;
    --popover-foreground: 220 25% 20%;
    --primary: 210 100% 50%;
    --primary-foreground: 0 0% 100%;
    --secondary: 210 20% 96%;
    --secondary-foreground: 220 25% 20%;
    --muted: 210 20% 96%;
    --muted-foreground: 220 10% 45%;
    --accent: 210 95% 95%;
    --accent-foreground: 210 100% 50%;
    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 100%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 210 100% 50%;
    --chart-1: 210 100% 50%;
    --chart-2: 262 83% 58%;
    --chart-3: 291 70% 50%;
    --chart-4: 316 70% 50%;
    --chart-5: 340 65% 55%;
    --radius: 0.8rem;
    --sidebar-background: 210 30% 98%;
    --sidebar-foreground: 220 25% 20%;
    --sidebar-primary: 210 100% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 210 95% 95%;
    --sidebar-accent-foreground: 210 100% 50%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 210 100% 50%;
  }
  
  /* Dark theme */
  [data-theme="dark"] {
    /* Apple-inspired dark mode color palette */
    --background: 220 20% 10%;
    --foreground: 210 15% 95%;
    --card: 220 20% 13%;
    --card-foreground: 210 15% 95%;
    --popover: 220 20% 13%;
    --popover-foreground: 210 15% 95%;
    --primary: 210 100% 60%;
    --primary-foreground: 220 20% 10%;
    --secondary: 215 25% 20%;
    --secondary-foreground: 210 15% 95%;
    --muted: 215 25% 20%;
    --muted-foreground: 210 10% 70%;
    --accent: 215 25% 20%;
    --accent-foreground: 210 15% 95%;
    --destructive: 0 70% 55%;
    --destructive-foreground: 210 15% 95%;
    --border: 215 25% 20%;
    --input: 215 25% 20%;
    --ring: 210 100% 60%;
    --chart-1: 210 100% 60%;
    --chart-2: 262 83% 68%;
    --chart-3: 291 70% 60%;
    --chart-4: 316 70% 60%;
    --chart-5: 340 65% 65%;
    --sidebar-background: 220 20% 13%;
    --sidebar-foreground: 210 15% 95%;
    --sidebar-primary: 210 100% 60%;
    --sidebar-primary-foreground: 220 20% 10%;
    --sidebar-accent: 215 25% 20%;
    --sidebar-accent-foreground: 210 15% 95%;
    --sidebar-border: 215 25% 20%;
    --sidebar-ring: 210 100% 60%;
  }
  
  /* Blue theme */
  [data-theme="blue"] {
    --background: 210 50% 98%;
    --foreground: 220 25% 20%;
    --card: 0 0% 100%;
    --card-foreground: 220 25% 20%;
    --popover: 0 0% 100%;
    --popover-foreground: 220 25% 20%;
    --primary: 210 100% 50%;
    --primary-foreground: 0 0% 100%;
    --secondary: 210 50% 96%;
    --secondary-foreground: 220 25% 20%;
    --muted: 210 50% 96%;
    --muted-foreground: 220 10% 45%;
    --accent: 210 95% 95%;
    --accent-foreground: 210 100% 50%;
    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 100%;
    --border: 210 30% 91%;
    --input: 210 30% 91%;
    --ring: 210 100% 50%;
    --chart-1: 210 100% 50%;
    --chart-2: 210 83% 58%;
    --chart-3: 210 70% 50%;
    --chart-4: 210 70% 50%;
    --chart-5: 210 65% 55%;
    --sidebar-background: 210 50% 98%;
    --sidebar-foreground: 220 25% 20%;
    --sidebar-primary: 210 100% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 210 95% 95%;
    --sidebar-accent-foreground: 210 100% 50%;
    --sidebar-border: 210 30% 91%;
    --sidebar-ring: 210 100% 50%;
  }
  
  /* Green theme */
  [data-theme="green"] {
    --background: 150 50% 98%;
    --foreground: 160 25% 20%;
    --card: 0 0% 100%;
    --card-foreground: 160 25% 20%;
    --popover: 0 0% 100%;
    --popover-foreground: 160 25% 20%;
    --primary: 150 60% 40%;
    --primary-foreground: 0 0% 100%;
    --secondary: 150 50% 96%;
    --secondary-foreground: 160 25% 20%;
    --muted: 150 50% 96%;
    --muted-foreground: 160 10% 45%;
    --accent: 150 95% 95%;
    --accent-foreground: 150 60% 40%;
    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 100%;
    --border: 150 30% 91%;
    --input: 150 30% 91%;
    --ring: 150 60% 40%;
    --chart-1: 150 60% 40%;
    --chart-2: 150 83% 58%;
    --chart-3: 150 70% 50%;
    --chart-4: 150 70% 50%;
    --chart-5: 150 65% 55%;
    --sidebar-background: 150 50% 98%;
    --sidebar-foreground: 160 25% 20%;
    --sidebar-primary: 150 60% 40%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 150 95% 95%;
    --sidebar-accent-foreground: 150 60% 40%;
    --sidebar-border: 150 30% 91%;
    --sidebar-ring: 150 60% 40%;
  }
  
  /* Purple theme */
  [data-theme="purple"] {
    --background: 270 50% 98%;
    --foreground: 280 25% 20%;
    --card: 0 0% 100%;
    --card-foreground: 280 25% 20%;
    --popover: 0 0% 100%;
    --popover-foreground: 280 25% 20%;
    --primary: 270 75% 50%;
    --primary-foreground: 0 0% 100%;
    --secondary: 270 50% 96%;
    --secondary-foreground: 280 25% 20%;
    --muted: 270 50% 96%;
    --muted-foreground: 280 10% 45%;
    --accent: 270 95% 95%;
    --accent-foreground: 270 75% 50%;
    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 100%;
    --border: 270 30% 91%;
    --input: 270 30% 91%;
    --ring: 270 75% 50%;
    --chart-1: 270 75% 50%;
    --chart-2: 270 83% 58%;
    --chart-3: 270 70% 50%;
    --chart-4: 270 70% 50%;
    --chart-5: 270 65% 55%;
    --sidebar-background: 270 50% 98%;
    --sidebar-foreground: 280 25% 20%;
    --sidebar-primary: 270 75% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 270 95% 95%;
    --sidebar-accent-foreground: 270 75% 50%;
    --sidebar-border: 270 30% 91%;
    --sidebar-ring: 270 75% 50%;
  }
  
  /* Yellow theme */
  [data-theme="yellow"] {
    --background: 50 50% 98%;
    --foreground: 40 25% 20%;
    --card: 0 0% 100%;
    --card-foreground: 40 25% 20%;
    --popover: 0 0% 100%;
    --popover-foreground: 40 25% 20%;
    --primary: 40 90% 50%;
    --primary-foreground: 0 0% 100%;
    --secondary: 50 50% 96%;
    --secondary-foreground: 40 25% 20%;
    --muted: 50 50% 96%;
    --muted-foreground: 40 10% 45%;
    --accent: 50 95% 95%;
    --accent-foreground: 40 90% 50%;
    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 100%;
    --border: 50 30% 91%;
    --input: 50 30% 91%;
    --ring: 40 90% 50%;
    --chart-1: 40 90% 50%;
    --chart-2: 40 83% 58%;
    --chart-3: 40 70% 50%;
    --chart-4: 40 70% 50%;
    --chart-5: 40 65% 55%;
    --sidebar-background: 50 50% 98%;
    --sidebar-foreground: 40 25% 20%;
    --sidebar-primary: 40 90% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 50 95% 95%;
    --sidebar-accent-foreground: 40 90% 50%;
    --sidebar-border: 50 30% 91%;
    --sidebar-ring: 40 90% 50%;
  }
  
  /* Pink theme */
  [data-theme="pink"] {
    --background: 330 50% 98%;
    --foreground: 340 25% 20%;
    --card: 0 0% 100%;
    --card-foreground: 340 25% 20%;
    --popover: 0 0% 100%;
    --popover-foreground: 340 25% 20%;
    --primary: 330 85% 60%;
    --primary-foreground: 0 0% 100%;
    --secondary: 330 50% 96%;
    --secondary-foreground: 340 25% 20%;
    --muted: 330 50% 96%;
    --muted-foreground: 340 10% 45%;
    --accent: 330 95% 95%;
    --accent-foreground: 330 85% 60%;
    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 100%;
    --border: 330 30% 91%;
    --input: 330 30% 91%;
    --ring: 330 85% 60%;
    --chart-1: 330 85% 60%;
    --chart-2: 330 83% 58%;
    --chart-3: 330 70% 50%;
    --chart-4: 330 70% 50%;
    --chart-5: 330 65% 55%;
    --sidebar-background: 330 50% 98%;
    --sidebar-foreground: 340 25% 20%;
    --sidebar-primary: 330 85% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 330 95% 95%;
    --sidebar-accent-foreground: 330 85% 60%;
    --sidebar-border: 330 30% 91%;
    --sidebar-ring: 330 85% 60%;
  }
  
  /* Orange theme */
  [data-theme="orange"] {
    --background: 30 50% 98%;
    --foreground: 20 25% 20%;
    --card: 0 0% 100%;
    --card-foreground: 20 25% 20%;
    --popover: 0 0% 100%;
    --popover-foreground: 20 25% 20%;
    --primary: 25 95% 50%;
    --primary-foreground: 0 0% 100%;
    --secondary: 30 50% 96%;
    --secondary-foreground: 20 25% 20%;
    --muted: 30 50% 96%;
    --muted-foreground: 20 10% 45%;
    --accent: 30 95% 95%;
    --accent-foreground: 25 95% 50%;
    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 100%;
    --border: 30 30% 91%;
    --input: 30 30% 91%;
    --ring: 25 95% 50%;
    --chart-1: 25 95% 50%;
    --chart-2: 25 83% 58%;
    --chart-3: 25 70% 50%;
    --chart-4: 25 70% 50%;
    --chart-5: 25 65% 55%;
    --sidebar-background: 30 50% 98%;
    --sidebar-foreground: 20 25% 20%;
    --sidebar-primary: 25 95% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 30 95% 95%;
    --sidebar-accent-foreground: 25 95% 50%;
    --sidebar-border: 30 30% 91%;
    --sidebar-ring: 25 95% 50%;
  }
  
  /* Teal theme */
  [data-theme="teal"] {
    --background: 180 50% 98%;
    --foreground: 190 25% 20%;
    --card: 0 0% 100%;
    --card-foreground: 190 25% 20%;
    --popover: 0 0% 100%;
    --popover-foreground: 190 25% 20%;
    --primary: 175 70% 40%;
    --primary-foreground: 0 0% 100%;
    --secondary: 180 50% 96%;
    --secondary-foreground: 190 25% 20%;
    --muted: 180 50% 96%;
    --muted-foreground: 190 10% 45%;
    --accent: 180 95% 95%;
    --accent-foreground: 175 70% 40%;
    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 100%;
    --border: 180 30% 91%;
    --input: 180 30% 91%;
    --ring: 175 70% 40%;
    --chart-1: 175 70% 40%;
    --chart-2: 175 83% 58%;
    --chart-3: 175 70% 50%;
    --chart-4: 175 70% 50%;
    --chart-5: 175 65% 55%;
    --sidebar-background: 180 50% 98%;
    --sidebar-foreground: 190 25% 20%;
    --sidebar-primary: 175 70% 40%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 180 95% 95%;
    --sidebar-accent-foreground: 175 70% 40%;
    --sidebar-border: 180 30% 91%;
    --sidebar-ring: 175 70% 40%;
  }
  
  /* Gray theme */
  [data-theme="gray"] {
    --background: 200 20% 98%;
    --foreground: 220 20% 20%;
    --card: 0 0% 100%;
    --card-foreground: 220 20% 20%;
    --popover: 0 0% 100%;
    --popover-foreground: 220 20% 20%;
    --primary: 220 25% 50%;
    --primary-foreground: 0 0% 100%;
    --secondary: 200 20% 96%;
    --secondary-foreground: 220 20% 20%;
    --muted: 200 20% 96%;
    --muted-foreground: 220 10% 45%;
    --accent: 200 20% 95%;
    --accent-foreground: 220 25% 50%;
    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 100%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 220 25% 50%;
    --chart-1: 220 25% 50%;
    --chart-2: 220 83% 58%;
    --chart-3: 220 70% 50%;
    --chart-4: 220 70% 50%;
    --chart-5: 220 65% 55%;
    --sidebar-background: 200 20% 98%;
    --sidebar-foreground: 220 20% 20%;
    --sidebar-primary: 220 25% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 200 20% 95%;
    --sidebar-accent-foreground: 220 25% 50%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 220 25% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-normal;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }

  /* Modern typography styles */
  h1, h2, h3, h4, h5, h6 {
    @apply font-medium tracking-tight;
  }

  h1 {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }

  h2 {
    @apply text-2xl md:text-3xl lg:text-4xl;
  }

  h3 {
    @apply text-xl md:text-2xl lg:text-3xl;
  }

  h4 {
    @apply text-lg md:text-xl;
  }

  p {
    @apply leading-relaxed;
  }

  /* Improved focus styles */
  :focus-visible {
    @apply outline-none ring-2 ring-primary/50 ring-offset-2 ring-offset-background transition-all;
  }
}

@layer utilities {
  /* Apple-style font weight utilities */
  .font-regular {
    font-weight: 400;
  }

  .font-medium {
    font-weight: 500;
  }

  .font-semibold {
    font-weight: 600;
  }

  /* Apple-style shimmer effect */
  .animate-shimmer {
    animation: shimmer 2s linear infinite;
    background-size: 200% 100%;
  }

  .animate-apple-shimmer {
    animation: apple-shimmer 2.5s cubic-bezier(0.4, 0.0, 0.2, 1) infinite;
    background-size: 200% 100%;
    background-image: linear-gradient(to right,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
  }

  .animate-apple-fade {
    animation: apple-fade 0.5s cubic-bezier(0.2, 0.8, 0.2, 1);
  }

  .animate-apple-scale {
    animation: apple-scale 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
  }

  /* Apple-inspired glass effect */
  .glass {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.05);
  }

  .dark .glass {
    background: rgba(30, 30, 35, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.08);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.2);
  }

  /* Apple-inspired hover effect for cards */
  .hover-lift {
    transition: transform 0.3s cubic-bezier(0.2, 0.8, 0.2, 1), box-shadow 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
  }

  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 14px 28px rgba(0, 0, 0, 0.08), 0 10px 10px rgba(0, 0, 0, 0.04);
  }

  /* Apple-inspired gradient text */
  .gradient-text {
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    background-image: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--chart-2)));
    font-weight: 600;
  }

  /* Apple-inspired soft shadow */
  .shadow-soft {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06), 0 2px 6px rgba(0, 0, 0, 0.04);
  }

  .dark .shadow-soft {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0, 0, 0, 0.15);
  }

  /* Apple-inspired animated gradient background */
  .animated-gradient {
    background: linear-gradient(-45deg,
      hsl(var(--primary) / 0.9),
      hsl(var(--chart-2) / 0.9),
      hsl(var(--chart-3) / 0.9),
      hsl(var(--chart-4) / 0.9));
    background-size: 300% 300%;
    animation: gradient 18s cubic-bezier(0.4, 0.0, 0.2, 1) infinite;
  }

  /* Apple-style button */
  .apple-button {
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    border-radius: calc(var(--radius) * 1.5);
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
  }

  .apple-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    background: hsl(var(--primary) / 0.9);
  }

  /* Apple-style card */
  .apple-card {
    background: hsl(var(--card));
    border-radius: calc(var(--radius) * 1.2);
    border: 1px solid hsl(var(--border) / 0.8);
    padding: 1.5rem;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
    transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
  }

  .dark .apple-card {
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2), 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  /* Floating chat input styles */
  .floating-chat-input {
    transform: translateY(0);
    transition: transform 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
  }

  .floating-chat-input.hidden {
    transform: translateY(100%);
  }

  /* Chat input container with floating effect */
  .chat-input-container {
    position: relative;
    transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
  }

  .chat-input-container:before {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 5%;
    right: 5%;
    height: 10px;
    background: transparent;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .chat-input-container:hover:before {
    opacity: 1;
  }

  .dark .chat-input-container:before {
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
  }
}

@keyframes shimmer {
  from {
    background-position: 200% 0;
  }
  to {
    background-position: -200% 0;
  }
}

/* Apple-style smooth animation for shimmer effect */
@keyframes apple-shimmer {
  0% {
    background-position: 100% 0;
    opacity: 0.8;
  }
  100% {
    background-position: -100% 0;
    opacity: 1;
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Apple-style smooth fade animation */
@keyframes apple-fade {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Apple-style scale animation */
@keyframes apple-scale {
  0% {
    transform: scale(0.95);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Typing animation effect */
@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

@keyframes blink-caret {
  from, to { border-color: transparent }
  50% { border-color: hsl(var(--primary)) }
}

.animate-typing-effect {
  display: inline-block;
  width: 0;
  overflow: hidden;
  white-space: nowrap;
  animation:
    typing 2.5s steps(40, end) forwards,
    blink-caret .75s step-end infinite;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Modern text selection styling */
::selection {
  @apply bg-primary/30 text-foreground;
}

/* For Firefox */
::-moz-selection {
  @apply bg-primary/30 text-foreground;
}

/* Selection in dark mode */
.dark ::selection {
  @apply bg-primary/40 text-foreground;
}

.dark ::-moz-selection {
  @apply bg-primary/40 text-foreground;
}

/* Markdown Content Styling */
.markdown-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Emergency streaming styles with highest priority */
@keyframes instant-show {
  0% { opacity: 1; }
  100% { opacity: 1; }
}

.urgent-streaming-styles {
  opacity: 1 !important;
  visibility: visible !important;
  animation: none !important;
  transition: none !important;
}

.urgent-streaming-styles * {
  opacity: 1 !important;
  visibility: visible !important;
  animation: instant-show 0.001s !important;
  transition: none !important;
}

.urgent-md-h1,
.urgent-md-h2,
.urgent-md-h3,
.urgent-md-h4 {
  color: hsl(var(--foreground)) !important;
  font-weight: bold !important;
  display: block !important;
  margin-bottom: 1rem !important;
}

.urgent-md-h1 {
  font-size: 1.5rem !important;
  margin-top: 1.5rem !important;
  padding-bottom: 0.25rem !important;
  border-bottom: 1px solid hsl(var(--border) / 0.4) !important;
}

.urgent-md-h2 {
  font-size: 1.25rem !important;
  margin-top: 1.25rem !important;
  padding-bottom: 0.25rem !important;
  border-bottom: 1px solid hsl(var(--border) / 0.3) !important;
}

.urgent-md-h3 {
  font-size: 1.125rem !important;
  margin-top: 1rem !important;
}

.urgent-md-h4 {
  font-size: 1rem !important;
  margin-top: 0.75rem !important;
}

/* Specific streaming styles to ensure consistent rendering */
.streaming-markdown-container {
  overflow: visible;
  width: 100%;
}

.streaming-content {
  /* Force immediate style application with higher specificity */
  width: 100% !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.streaming-content > * {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Custom streaming markdown class */
.custom-streaming-markdown {
  width: 100% !important;
  color: hsl(var(--foreground)) !important;
  line-height: 1.65 !important;
  overflow-wrap: break-word !important;
}

.custom-streaming-markdown h1 {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  margin-top: 1.5rem !important;
  margin-bottom: 1rem !important;
  padding-bottom: 0.25rem !important;
  border-bottom: 1px solid hsl(var(--border) / 0.4) !important;
  color: hsl(var(--foreground)) !important;
}

.custom-streaming-markdown h2 {
  font-size: 1.25rem !important;
  font-weight: 700 !important;
  margin-top: 1.25rem !important;
  margin-bottom: 0.75rem !important;
  padding-bottom: 0.25rem !important;
  border-bottom: 1px solid hsl(var(--border) / 0.3) !important;
  color: hsl(var(--foreground)) !important;
}

.custom-streaming-markdown h3 {
  font-size: 1.125rem !important;
  font-weight: 700 !important;
  margin-top: 1rem !important;
  margin-bottom: 0.5rem !important;
  color: hsl(var(--foreground)) !important;
}

.custom-streaming-markdown h4 {
  font-size: 1rem !important;
  font-weight: 700 !important;
  margin-top: 0.75rem !important;
  margin-bottom: 0.5rem !important;
  color: hsl(var(--foreground)) !important;
}

.custom-streaming-markdown p {
  margin-bottom: 1rem !important;
  white-space: pre-wrap !important;
}

.custom-streaming-markdown ul,
.custom-streaming-markdown ol {
  padding-left: 1.5rem !important;
  margin-bottom: 1rem !important;
}

.custom-streaming-markdown ul {
  list-style-type: disc !important;
}

.custom-streaming-markdown ol {
  list-style-type: decimal !important;
}

.custom-streaming-markdown li {
  margin-bottom: 0.25rem !important;
}

.custom-streaming-markdown pre {
  padding: 1rem !important;
  border-radius: 0.5rem !important;
  background-color: hsl(var(--muted)) !important;
  overflow-x: auto !important;
  font-size: 0.875rem !important;
  margin: 1rem 0 !important;
}

.custom-streaming-markdown code {
  background-color: hsl(var(--muted)) !important;
  padding: 0.125rem 0.375rem !important;
  border-radius: 0.25rem !important;
  font-size: 0.875rem !important;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace !important;
}

.custom-streaming-markdown blockquote {
  margin: 1rem 0 !important;
  padding-left: 1rem !important;
  border-left: 2px solid hsl(var(--primary) / 0.3) !important;
  color: hsl(var(--muted-foreground)) !important;
  font-style: italic !important;
}

/* Ensure streaming elements receive styling immediately */
.streaming-content h1,
.streaming-content h2,
.streaming-content h3,
.streaming-content h4,
.streaming-content h5,
.streaming-content h6,
.streaming-content p,
.streaming-content ul,
.streaming-content ol,
.streaming-content pre,
.streaming-content blockquote,
.streaming-content table {
  animation: none !important;
  transition: none !important;
}

.prose-content {
  width: 100%;
  color: hsl(var(--foreground));
  line-height: 1.65;
  overflow-wrap: break-word;
}

.prose-content > *:first-child {
  margin-top: 0;
}

.prose-content > *:last-child {
  margin-bottom: 0;
}

.prose-content h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  padding-bottom: 0.25rem;
  border-bottom: 1px solid hsl(var(--border) / 0.4);
  color: hsl(var(--foreground));
}

.prose-content h2 {
  font-size: 1.25rem;
  font-weight: 700;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
  padding-bottom: 0.25rem;
  border-bottom: 1px solid hsl(var(--border) / 0.3);
  color: hsl(var(--foreground));
}

.prose-content h3 {
  font-size: 1.125rem;
  font-weight: 700;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  color: hsl(var(--foreground));
}

.prose-content h4 {
  font-size: 1rem;
  font-weight: 700;
  margin-top: 0.75rem;
  margin-bottom: 0.5rem;
  color: hsl(var(--foreground));
}

.prose-content ul,
.prose-content ol {
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

.prose-content ul {
  list-style-type: disc;
}

.prose-content ol {
  list-style-type: decimal;
}

.prose-content li {
  margin-bottom: 0.25rem;
}

.prose-content ul > li:not(:last-child),
.prose-content ol > li:not(:last-child) {
  margin-bottom: 0.25rem;
}

.prose-content p {
  margin-bottom: 1rem;
  white-space: pre-wrap;
}

.prose-content a {
  color: hsl(var(--primary));
  text-decoration: underline;
  text-underline-offset: 2px;
  transition: color 0.15s ease;
}

.prose-content a:hover {
  color: hsl(var(--primary) / 0.8);
}

.prose-content blockquote {
  margin: 1rem 0;
  padding-left: 1rem;
  border-left: 2px solid hsl(var(--primary) / 0.3);
  color: hsl(var(--muted-foreground));
  font-style: italic;
}

.prose-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
  margin: 1rem 0;
  border: 1px solid hsl(var(--border) / 0.5);
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.prose-content pre {
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: hsl(var(--muted));
  overflow-x: auto;
  font-size: 0.875rem;
  margin: 1rem 0;
}

.prose-content pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  color: inherit;
  font-size: 0.875rem;
}

.prose-content code {
  background-color: hsl(var(--muted));
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.prose-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
  font-size: 0.875rem;
}

.prose-content table th {
  background-color: hsl(var(--muted) / 0.5);
  border: 1px solid hsl(var(--border));
  padding: 0.5rem;
  text-align: left;
  font-weight: 500;
}

.prose-content table td {
  border: 1px solid hsl(var(--border));
  padding: 0.5rem;
}

.prose-content hr {
  margin: 1.5rem 0;
  border: 0;
  border-top: 1px solid hsl(var(--border));
}