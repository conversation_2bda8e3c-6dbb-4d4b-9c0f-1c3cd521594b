"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { Send, Upload, X, <PERSON>ader2, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Brain, Binoculars } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import TextareaAutosize from "react-textarea-autosize"
import { CommandMenu } from "./command-menu"
import { EmojiPicker } from "./emoji-picker"
import { ModelSelector } from "@/components/page/main/model-selector"

interface ChatInputProps {
  message: string
  onMessageChange: (message: string) => void
  onSubmit: (e: React.FormEvent) => void
  onStop?: () => void
  onToggleFileUpload: () => void
  isLoading: boolean
  isDisabled: boolean
  isProcessingFiles: boolean
  uploadedFilesCount: number
  showFileUpload: boolean
  onClearFiles: () => void
  placeholder?: string
  maxLength?: number
  onCommandExecute?: (command: string) => void
  onKeyDown?: (e: React.KeyboardEvent) => void
  selectedModel?: string
  onModelChange?: (model: string) => void
  models?: any[]
  browseMode?: boolean
  onBrowseModeChange?: (value: boolean) => void
  reasoning?: boolean
  onReasoningChange?: (value: boolean) => void
  research?: boolean
  onResearchChange?: (value: boolean) => void
}

export function ChatInput({
  message,
  onMessageChange,
  onSubmit,
  onStop,
  onToggleFileUpload,
  isLoading,
  isDisabled,
  isProcessingFiles,
  uploadedFilesCount,
  showFileUpload,
  onClearFiles,
  placeholder = "Type your message...",
  onCommandExecute,
  onKeyDown,
  selectedModel,
  onModelChange,
  models,
  browseMode,
  onBrowseModeChange,
  reasoning,
  onReasoningChange,
  research,
  onResearchChange,
}: ChatInputProps) {
  const inputRef = useRef<HTMLTextAreaElement>(null)
  const commandMenuContainerRef = useRef<HTMLDivElement>(null)
  const [isFocused, setIsFocused] = useState(false)
  const [isRecording, setIsRecording] = useState(false)
  const [showCommandMenu, setShowCommandMenu] = useState(false)
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null)
  const [recordingTime, setRecordingTime] = useState(0)
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null)

  // Focus the input when the component mounts
  useEffect(() => {
    if (inputRef.current && !isDisabled) {
      inputRef.current.focus()
    }
  }, [isDisabled])

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Escape key to close command menu
      if (e.key === "Escape" && showCommandMenu) {
        setShowCommandMenu(false)
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [showCommandMenu])

  // Handle clicks outside the command menu to close it
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (
        showCommandMenu &&
        commandMenuContainerRef.current &&
        !commandMenuContainerRef.current.contains(e.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(e.target as Node)
      ) {
        setShowCommandMenu(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [showCommandMenu])

  // Add a separate effect to detect slash commands
  useEffect(() => {
    // Check if message starts with a slash
    if (message.startsWith("/")) {
      setShowCommandMenu(true)
    } else {
      setShowCommandMenu(false)
    }
  }, [message])

  // Handle recording timer
  useEffect(() => {
    if (isRecording) {
      recordingTimerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1)
      }, 1000)
    } else {
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current)
        recordingTimerRef.current = null
      }
      setRecordingTime(0)
    }

    return () => {
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current)
      }
    }
  }, [isRecording])

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Allow parent component to handle keyboard events first
    if (onKeyDown) {
      onKeyDown(e);
      // If the event was prevented by the parent handler, don't continue
      if (e.defaultPrevented) {
        return;
      }
    }

    // Submit on Enter without Shift key
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      const form = e.currentTarget.form
      if (form) form.dispatchEvent(new Event("submit", { cancelable: true, bubbles: true }))
    }

    // Open command menu when typing slash at the beginning of the input
    if (e.key === "/" && e.currentTarget.selectionStart === 0) {
      setShowCommandMenu(true)
    }

    // Format text with keyboard shortcuts
    if (e.ctrlKey) {
      switch (e.key) {
        case "b": // Bold
          e.preventDefault()
          applyFormat("**")
          break
        case "i": // Italic
          e.preventDefault()
          applyFormat("_")
          break
        case "e": // Code
          e.preventDefault()
          applyFormat("`")
          break
        case "k": // Link
          e.preventDefault()
          applyFormat("[](url)")
          break
      }
    }
  }

  // Apply formatting to selected text or insert at cursor
  const applyFormat = (format: string) => {
    if (!inputRef.current) return

    const input = inputRef.current
    const start = input.selectionStart
    const end = input.selectionEnd
    const selectedText = message.substring(start, end)

    let newText = message
    let newCursorPos = start

    if (format === "```\n") {
      // Handle code block
      newText = message.substring(0, start) + "```\n" + selectedText + "\n```" + message.substring(end)
      newCursorPos = start + 4 + selectedText.length
    } else if (format === "[](url)") {
      // Handle link
      if (selectedText) {
        newText = message.substring(0, start) + "[" + selectedText + "](url)" + message.substring(end)
        newCursorPos = start + selectedText.length + 3
      } else {
        newText = message.substring(0, start) + "[](url)" + message.substring(end)
        newCursorPos = start + 1
      }
    } else if (format === "- " || format === "1. " || format === "> ") {
      // Handle lists and quotes (line start)
      const lineStart = message.lastIndexOf("\n", start - 1) + 1
      newText = message.substring(0, lineStart) + format + message.substring(lineStart)
      newCursorPos = lineStart + format.length + (end - start)
    } else {
      // Handle inline formatting (bold, italic, code)
      newText = message.substring(0, start) + format + selectedText + format + message.substring(end)
      newCursorPos = selectedText ? end + 2 * format.length : start + format.length
    }

    onMessageChange(newText)

    // Set cursor position after state update
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus()
        inputRef.current.setSelectionRange(newCursorPos, newCursorPos)
      }
    }, 0)
  }

  // Handle emoji selection
  const handleEmojiSelect = (emoji: string) => {
    if (!inputRef.current) return

    const input = inputRef.current
    const start = input.selectionStart
    const end = input.selectionEnd

    const newText = message.substring(0, start) + emoji + message.substring(end)
    onMessageChange(newText)

    // Set cursor position after the inserted emoji
    setTimeout(() => {
      if (inputRef.current) {
        const newPosition = start + emoji.length
        inputRef.current.focus()
        inputRef.current.setSelectionRange(newPosition, newPosition)
      }
    }, 0)
  }

  // Handle command selection
  const handleCommandSelect = (command: string) => {
    onMessageChange(command + " ")
    setShowCommandMenu(false)

    // Focus the input after selecting a command
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus()
      }
    }, 0)

    if (onCommandExecute) {
      onCommandExecute(command)
    }
  }

  // Handle voice recording
  const toggleRecording = async () => {
    if (isRecording) {
      // Stop recording
      if (mediaRecorder && mediaRecorder.state === "recording") {
        mediaRecorder.stop()
      }
      setIsRecording(false)
    } else {
      try {
        // Start recording
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
        const recorder = new MediaRecorder(stream, { mimeType: 'audio/webm' })
        const audioChunks: BlobPart[] = []

        recorder.addEventListener("dataavailable", (event) => {
          audioChunks.push(event.data)
        })

        recorder.addEventListener("stop", async () => {
          const audioBlob = new Blob(audioChunks, { type: "audio/webm" })

          try {
            // Send the audio blob to our API endpoint
            const formData = new FormData()
            formData.append("audio", audioBlob)

            const response = await fetch("/api/transcribe/stt", {
              method: "POST",
              body: formData,
            })

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`)
            }

            const data = await response.json()
            if (data.error) {
              throw new Error(data.error)
            }

            // Add transcription to message
            const transcription = data.transcript.trim()
            if (transcription) {
              onMessageChange(message + (message ? " " : "") + transcription)
            }
          } catch (error) {
            console.error("Transcription error:", error)
            alert("Failed to transcribe audio. Please try again.")
          } finally {
            // Clean up
            stream.getTracks().forEach(track => track.stop())
            setMediaRecorder(null)
          }
        })

        recorder.start()
        setMediaRecorder(recorder)
        setIsRecording(true)
      } catch (err) {
        console.error("Error accessing microphone:", err)
        alert("Could not access your microphone. Please check permissions.")
      }
    }
  }

  // Handle image paste
  const handlePaste = (e: React.ClipboardEvent) => {
    const items = e.clipboardData.items

    for (let i = 0; i < items.length; i++) {
      if (items[i].type.indexOf("image") !== -1) {
        // We have an image! Let's process it
        const file = items[i].getAsFile()
        if (file) {
          // Prevent the default paste behavior for images
          e.preventDefault()

          // Here you would typically handle the image file
          // For example, you might want to upload it or add it to your files array
          alert("Image pasted! This would typically be uploaded.")
          break
        }
      }
    }
  }

  // Format recording time
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  return (
    <TooltipProvider>
      {/* Model selector (shows only if props provided) */}
      {onModelChange && models && (
        <div className="mb-3 flex justify-start">
          <ModelSelector value={selectedModel ?? ""} onChange={onModelChange} />
        </div>
      )}

      <div className="animate-apple-fade max-w-4xl mx-auto px-2 sm:px-0">
        <form onSubmit={onSubmit} className="w-full relative">
          {/* Main Input Container */}
          <div
            className={cn(
              "relative flex items-end gap-2 sm:gap-3 p-3 sm:p-4 bg-background rounded-2xl sm:rounded-3xl transition-all duration-300",
              "border-2 shadow-lg",
              isFocused ? "border-primary/30 shadow-xl ring-4 ring-primary/10" : "border-border/50 hover:border-border",
              isDisabled && "opacity-60",
            )}
          >
            {/* Command Menu */}
            <div ref={commandMenuContainerRef}>
              {showCommandMenu && (
                <div className="absolute left-4 bottom-full mb-2">
                  <CommandMenu onCommandSelect={handleCommandSelect} isOpen={showCommandMenu} inputValue={message} />
                </div>
              )}
            </div>

            {/* File Upload Button */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "h-8 w-8 sm:h-10 sm:w-10 rounded-full transition-all duration-200 flex-shrink-0",
                    uploadedFilesCount > 0
                      ? "bg-primary/10 text-primary hover:bg-primary/20"
                      : "text-muted-foreground hover:text-foreground hover:bg-muted/50",
                  )}
                  onClick={onToggleFileUpload}
                >
                  {uploadedFilesCount > 0 ? (
                    <div className="relative">
                      <Upload className="w-4 h-4 sm:w-5 sm:h-5" />
                      <span className="absolute -top-1 -right-1 flex h-3 w-3 sm:h-4 sm:w-4 items-center justify-center rounded-full bg-primary text-[8px] sm:text-[10px] text-primary-foreground font-bold">
                        {uploadedFilesCount}
                      </span>
                    </div>
                  ) : (
                    <Upload className="w-4 h-4 sm:w-5 sm:h-5" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top">{showFileUpload ? "Hide file upload" : "Attach files"}</TooltipContent>
            </Tooltip>

            {/* Text Input */}
            <div className="flex-1 min-w-0">
              <TextareaAutosize
                ref={inputRef}
                value={message}
                onChange={(e) => onMessageChange(e.target.value)}
                onKeyDown={handleKeyDown}
                onPaste={handlePaste}
                placeholder={isRecording ? "Listening..." : placeholder}
                disabled={isDisabled || isRecording}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(false)}
                className={cn(
                  "w-full bg-transparent border-0 resize-none focus:outline-none",
                  "text-sm sm:text-base placeholder:text-muted-foreground/60",
                  "min-h-[20px] sm:min-h-[24px] max-h-[100px] sm:max-h-[120px] py-1.5 sm:py-2",
                  "transition-all duration-300 ease-in-out",
                )}
                maxRows={5}
              />
            </div>

            {/* Right Side Actions */}
            <div className="flex items-center gap-0.5 sm:gap-1 flex-shrink-0">
              {/* Recording Indicator */}
              {isRecording && (
                <div className="flex items-center gap-1 sm:gap-2 mr-1 sm:mr-2 text-destructive animate-pulse">
                  <span className="text-xs font-medium hidden sm:inline">{formatTime(recordingTime)}</span>
                  <span className="h-2 w-2 rounded-full bg-destructive"></span>
                </div>
              )}

              {/* Emoji Picker */}
              <EmojiPicker onEmojiSelect={handleEmojiSelect} />

              {/* Voice Input */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className={cn(
                      "h-8 w-8 sm:h-10 sm:w-10 rounded-full transition-all duration-200",
                      isRecording
                        ? "text-destructive bg-destructive/10 hover:bg-destructive/20"
                        : "text-muted-foreground hover:text-foreground hover:bg-muted/50",
                    )}
                    onClick={toggleRecording}
                  >
                    {isRecording ? (
                      <MicOff className="h-4 w-4 sm:h-5 sm:w-5" />
                    ) : (
                      <Mic className="h-4 w-4 sm:h-5 sm:w-5" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top">{isRecording ? "Stop recording" : "Voice input"}</TooltipContent>
              </Tooltip>

              {/* Clear Files */}
              {uploadedFilesCount > 0 && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 sm:h-10 sm:w-10 rounded-full text-muted-foreground hover:text-foreground hover:bg-muted/50"
                      onClick={onClearFiles}
                    >
                      <X className="h-3 w-3 sm:h-4 sm:w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top">Clear all files</TooltipContent>
                </Tooltip>
              )}

              {/* Send Button */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    type="submit"
                    size="icon"
                    className={cn(
                      "h-8 w-8 sm:h-10 sm:w-10 rounded-full transition-all duration-300 ml-1 sm:ml-2",
                      message.trim() || uploadedFilesCount > 0
                        ? "bg-primary hover:bg-primary/90 shadow-lg hover:shadow-xl"
                        : "bg-primary/50 cursor-not-allowed",
                      "text-primary-foreground",
                    )}
                    disabled={
                      isLoading || isProcessingFiles || isDisabled || (message.trim() === "" && uploadedFilesCount === 0)
                    }
                  >
                    {isLoading || isProcessingFiles ? (
                      <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 animate-spin" />
                    ) : (
                      <Send className="w-4 h-4 sm:w-5 sm:h-5" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top">Send message</TooltipContent>
              </Tooltip>
            </div>
          </div>

          {/* File Upload Indicator */}
          {uploadedFilesCount > 0 && !showFileUpload && (
            <div className="mt-2 sm:mt-3 flex items-center justify-center">
              <div className="flex items-center gap-1.5 sm:gap-2 bg-primary/5 border border-primary/20 rounded-full px-2.5 sm:px-3 py-1 sm:py-1.5">
                <span className="text-xs font-medium text-primary">
                  {uploadedFilesCount} file{uploadedFilesCount !== 1 ? "s" : ""} attached
                </span>
                <Button
                  type="button"
                  variant="link"
                  size="sm"
                  className="h-auto p-0 text-xs text-primary hover:text-primary/80"
                  onClick={onToggleFileUpload}
                >
                  View
                </Button>
              </div>
            </div>
          )}
        </form>
      </div>
    </TooltipProvider>
  )
}
