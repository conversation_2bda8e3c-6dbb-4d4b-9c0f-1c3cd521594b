"use client"

import { Message } from "ai/react"
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Co<PERSON> } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { memo, useState, useEffect } from "react"
import { MarkdownRenderer } from "@/components/chat/Message/MarkdownRenderer"
import { CollapsibleUserMessage } from "@/components/chat/Message/CollapsibleUserMessage"
import { LoadingIndicator } from "@/components/chat/Message/LoadingIndicator"
import remarkGfm from "remark-gfm"
import remarkMath from "remark-math"

interface ChatMessageProps {
  message: Message
  isLast?: boolean
  onRegenerate?: () => void
  onEdit?: (messageId: string) => void
  onReport?: (messageId: string) => void
  onPlay?: (messageId: string) => void
  onRetry?: (messageId: string) => void
  onBranchOff?: (messageId: string) => void
  isEditing?: boolean
  onSaveEdit?: (messageId: string, content: string) => void
  onCancelEdit?: () => void
}

export const ChatMessage = memo(function ChatMessage({
  message,
  isEditing,
}: Readonly<ChatMessageProps>) {
  const { role, content } = message
  const isUser = role === "user"
  const isAssistant = role === "assistant"
  const isLoading = isAssistant && !content
  const [copied, setCopied] = useState(false)
  const [isDarkTheme, setIsDarkTheme] = useState(false)

  useEffect(() => {
    // Check for dark theme
    const isDark = document.documentElement.classList.contains('dark') || 
                   document.documentElement.getAttribute('data-theme') === 'dark'
    setIsDarkTheme(isDark)
  }, [])

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      },
      (err) => console.error("Could not copy text: ", err)
    )
  }

  const Avatar = isUser ? User : Bot;

  const renderContent = () => {
    if (isLoading) {
      return <LoadingIndicator />
    }
    if (content) {
       if (isUser) {
        return <CollapsibleUserMessage 
                  content={content} 
                  isDarkTheme={isDarkTheme}
                  remarkPlugins={[remarkGfm, remarkMath]}
                  copyToClipboard={copyToClipboard}
                  copied={copied}
               />
      }
      return <MarkdownRenderer 
                content={content} 
                isDarkTheme={isDarkTheme}
                remarkPlugins={[remarkGfm, remarkMath]}
                copyToClipboard={copyToClipboard}
                copied={copied}
             />
    }
    return null;
  }

  return (
    <div
      className={cn(
        "group/message relative mb-4 flex animate-fade-in-up items-start gap-3",
        "transition-all duration-300 ease-in-out"
      )}
    >
      <div
        className={cn(
          "flex h-8 w-8 shrink-0 items-center justify-center rounded-full",
          isUser
            ? "border bg-background"
            : "bg-primary text-primary-foreground"
        )}
      >
        <Avatar className={cn(isUser ? "h-5 w-5" : "h-6 w-6")} />
      </div>

      <div className="flex-1 space-y-1.5">
        <span className="text-sm font-semibold text-foreground/80">
          {isUser ? "You" : "ErzenAI"}
        </span>
        
        <div
          className={cn(
            "group/bubble relative rounded-2xl px-4 py-3",
            "transition-colors duration-200",
            isUser
              ? "bg-primary/5 text-primary-foreground"
              : "bg-muted/50",
            isEditing && "ring-2 ring-primary/50"
          )}
        >
          <div className="prose prose-neutral prose-sm dark:prose-invert max-w-none break-words leading-6">
            {renderContent()}
          </div>

          {content && !isLoading && (
            <div className="absolute bottom-1 right-2 flex items-center gap-1 opacity-0 transition-opacity duration-200 group-hover/bubble:opacity-100">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-7 w-7"
                      onClick={() => copyToClipboard(content)}
                    >
                      {copied ? (
                        <Check className="h-4 w-4 text-green-500" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Copy</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}) 