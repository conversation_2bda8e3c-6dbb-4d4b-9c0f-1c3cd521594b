"use client"

import { Message } from "ai/react"
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Co<PERSON> } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { memo, useState, useEffect } from "react"
import { MarkdownRenderer } from "@/components/chat/Message/MarkdownRenderer"
import { CollapsibleUserMessage } from "@/components/chat/Message/CollapsibleUserMessage"
import { LoadingIndicator } from "@/components/chat/Message/LoadingIndicator"
import remarkGfm from "remark-gfm"
import remarkMath from "remark-math"

interface ChatMessageProps {
  message: Message
  isLast?: boolean
  onRegenerate?: () => void
  onEdit?: (messageId: string) => void
  onReport?: (messageId: string) => void
  onPlay?: (messageId: string) => void
  onRetry?: (messageId: string) => void
  onBranchOff?: (messageId: string) => void
  isEditing?: boolean
  onSaveEdit?: (messageId: string, content: string) => void
  onCancelEdit?: () => void
}

export const ChatMessage = memo(function ChatMessage({
  message,
  isEditing,
}: Readonly<ChatMessageProps>) {
  const { role, content } = message
  const isUser = role === "user"
  const isAssistant = role === "assistant"
  const isLoading = isAssistant && !content
  const [copied, setCopied] = useState(false)
  const [isDarkTheme, setIsDarkTheme] = useState(false)

  useEffect(() => {
    // Check for dark theme
    const isDark = document.documentElement.classList.contains('dark') || 
                   document.documentElement.getAttribute('data-theme') === 'dark'
    setIsDarkTheme(isDark)
  }, [])

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      },
      (err) => console.error("Could not copy text: ", err)
    )
  }

  const Avatar = isUser ? User : Bot;

  const renderContent = () => {
    if (isLoading) {
      return <LoadingIndicator />
    }
    if (content) {
       if (isUser) {
        return <CollapsibleUserMessage 
                  content={content} 
                  isDarkTheme={isDarkTheme}
                  remarkPlugins={[remarkGfm, remarkMath]}
                  copyToClipboard={copyToClipboard}
                  copied={copied}
               />
      }
      return <MarkdownRenderer 
                content={content} 
                isDarkTheme={isDarkTheme}
                remarkPlugins={[remarkGfm, remarkMath]}
                copyToClipboard={copyToClipboard}
                copied={copied}
             />
    }
    return null;
  }

  return (
    <div
      className={cn(
        "group/message relative mb-6 flex animate-fade-in-up items-start gap-4",
        "transition-all duration-300 ease-in-out max-w-4xl mx-auto"
      )}
    >
      {/* Avatar */}
      <div
        className={cn(
          "flex h-10 w-10 shrink-0 items-center justify-center rounded-full border-2",
          isUser
            ? "bg-gradient-to-br from-blue-500 to-purple-600 border-blue-500/20 text-white"
            : "bg-gradient-to-br from-orange-500 to-red-600 border-orange-500/20 text-white"
        )}
      >
        <Avatar className="h-5 w-5" />
      </div>

      {/* Message Content */}
      <div className="flex-1 space-y-2 min-w-0">
        {/* Username */}
        <div className="flex items-center gap-2">
          <span className="text-sm font-semibold text-foreground">
            {isUser ? "You" : "Grok"}
          </span>
          <span className="text-xs text-muted-foreground">
            {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </span>
        </div>

        {/* Message Bubble */}
        <div
          className={cn(
            "group/bubble relative rounded-2xl px-4 py-3 max-w-none",
            "transition-all duration-200 border",
            isUser
              ? "bg-muted/30 border-muted/50 text-foreground"
              : "bg-background border-border/50",
            isEditing && "ring-2 ring-primary/50"
          )}
        >
          <div className="prose prose-neutral prose-sm dark:prose-invert max-w-none break-words leading-relaxed">
            {renderContent()}
          </div>

          {/* Action Buttons */}
          {content && !isLoading && (
            <div className="flex items-center gap-1 mt-3 pt-2 border-t border-border/30 opacity-60 group-hover/bubble:opacity-100 transition-opacity duration-200">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 px-3 text-xs text-muted-foreground hover:text-foreground"
                      onClick={() => copyToClipboard(content)}
                    >
                      {copied ? (
                        <>
                          <Check className="h-3 w-3 mr-1 text-green-500" />
                          Copied
                        </>
                      ) : (
                        <>
                          <Copy className="h-3 w-3 mr-1" />
                          Copy
                        </>
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Copy message</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}) 