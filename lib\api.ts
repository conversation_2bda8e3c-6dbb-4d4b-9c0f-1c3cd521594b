import axios from "axios";
import { ChatThread } from "./types";
import type {
  AIModel,
  UserInfo,
  ChatMemory,
  UserInstruction,
  CurrentVersion,
  Message,
  Agent,
  AgentStep,
  AgentExecution,
  Project,
  ProjectFile,
} from "./types";

export async function updateThreadMessage(
  threadId: string,
  messageId: string,
  data: { content: string }
) {
  await axios.patch(`/api/threads/${threadId}/messages/${messageId}`, data);
}

export async function retryThreadMessage(threadId: string, messageId: string) {
  await axios.post(`/api/threads/${threadId}/messages/${messageId}/retry`);
}

export async function branchThreadAtMessage(
  threadId: string,
  messageId: string
): Promise<ChatThread> {
  const res = await axios.post<ChatThread>(`/api/threads/${threadId}/branch`, {
    messageId,
  });
  return res.data;
}

// -----------------------------------------------------------------------------
// NEW GENERIC HELPERS (non-agent endpoints)
// These are thin wrappers around Next.js API routes. They make the client
// code compile and provide a single place to tweak the URL/shape later.
// -----------------------------------------------------------------------------

const axiosInstance = axios.create({
  // In the browser this is relative to the same origin. On the server it will
  // also work provided the URL is absolute or you proxy requests.
  baseURL: "",
  withCredentials: true,
});

/* -------------------------------------------------------------------------- */
/* User endpoints                                                              */
/* -------------------------------------------------------------------------- */
export async function fetchUserInfo(): Promise<UserInfo> {
  const res = await axiosInstance.get<UserInfo>("/api/user/info");
  return res.data;
}

/* -------------------------------------------------------------------------- */
/* Models                                                                      */
/* -------------------------------------------------------------------------- */
export async function fetchModels(): Promise<AIModel[]> {
  const res = await axiosInstance.get<AIModel[]>("/api/models");
  return res.data;
}

/* -------------------------------------------------------------------------- */
/* Usage                                                                       */
/* -------------------------------------------------------------------------- */
export async function fetchUsage(): Promise<any> {
  const res = await axiosInstance.get("/api/usage");
  return res.data;
}

/* -------------------------------------------------------------------------- */
/* Memory                                                                      */
/* -------------------------------------------------------------------------- */
export async function fetchMemory(): Promise<ChatMemory[]> {
  const res = await axiosInstance.get<ChatMemory[]>("/api/memory");
  return res.data;
}

export async function deleteAllMemory(): Promise<void> {
  await axiosInstance.delete("/api/memory");
}

/* -------------------------------------------------------------------------- */
/* Instructions                                                                */
/* -------------------------------------------------------------------------- */
export async function fetchInstructions(): Promise<UserInstruction[]> {
  const res = await axiosInstance.get<UserInstruction[]>("/api/instructions");
  return res.data;
}

export async function addInstruction(job: string): Promise<UserInstruction> {
  const res = await axiosInstance.post<UserInstruction>("/api/instructions", {
    job,
  });
  return res.data;
}

export async function updateInstruction(
  id: string,
  job: string
): Promise<UserInstruction> {
  const res = await axiosInstance.patch<UserInstruction>(
    `/api/instructions/${id}`,
    { job }
  );
  return res.data;
}

export async function deleteInstruction(id: string): Promise<void> {
  await axiosInstance.delete(`/api/instructions/${id}`);
}

/* -------------------------------------------------------------------------- */
/* Projects – file versions + CRUD                                             */
/* -------------------------------------------------------------------------- */
export async function fetchProjectFileVersions(
  projectId: string,
  fileId: string
): Promise<CurrentVersion[]> {
  const res = await axiosInstance.get<CurrentVersion[]>(
    `/api/projects/${projectId}/files/${fileId}/versions`
  );
  return res.data;
}

export async function revertProjectFileVersion(
  projectId: string,
  fileId: string,
  data: { version: number; commitMsg?: string }
): Promise<void> {
  await axiosInstance.post(
    `/api/projects/${projectId}/files/${fileId}/versions/${data.version}/revert`,
    { commitMsg: data.commitMsg }
  );
}

export async function createProjectFile(
  projectId: string,
  data: {
    name: string;
    path: string;
    content: string;
    commitMsg?: string;
  }
): Promise<void> {
  await axiosInstance.post(`/api/projects/${projectId}/files`, data);
}

export async function updateProjectFile(
  projectId: string,
  fileId: string,
  data: {
    content: string;
    commitMsg?: string;
  }
): Promise<void> {
  await axiosInstance.patch(`/api/projects/${projectId}/files/${fileId}`, data);
}

/* -------------------------------------------------------------------------- */
/* Threads – messages                                                          */
/* -------------------------------------------------------------------------- */
export async function fetchThreadMessages(
  threadId: string
): Promise<Message[]> {
  const res = await axiosInstance.get<Message[]>(
    `/api/threads/${threadId}/messages`
  );
  return res.data;
}

/* -------------------------------------------------------------------------- */
/* Text Input Completions                                                     */
/* -------------------------------------------------------------------------- */

export async function getTextInputCompletions(
  prompt: string,
  maxSuggestions: number = 10
): Promise<string[]> {
  try {
    const res = await axiosInstance.post<{ completions: string[] }>(
      "/api/completions",
      {
        prompt,
        maxSuggestions,
      }
    );
    return res.data.completions;
  } catch (error) {
    console.error("Failed to fetch completions", error);
    return [];
  }
}

// -----------------------------------------------------------------------------
// Agents – CRUD & execution helpers
// -----------------------------------------------------------------------------

/* -------------------------------------------------------------------------- */
/* Agent CRUD                                                                  */
/* -------------------------------------------------------------------------- */

export async function fetchAgents(): Promise<Agent[]> {
  const res = await axiosInstance.get<Agent[]>("/api/agents");
  return res.data;
}

export async function fetchAgent(agentId: string): Promise<Agent> {
  const res = await axiosInstance.get<Agent>(`/api/agents/${agentId}`);
  return res.data;
}

export async function createAgent(data: {
  name: string;
  description?: string;
}): Promise<Agent> {
  const res = await axiosInstance.post<Agent>("/api/agents", data);
  return res.data;
}

export async function updateAgent(
  agentId: string,
  data: { name?: string; description?: string }
): Promise<Agent> {
  const res = await axiosInstance.patch<Agent>(`/api/agents/${agentId}`, data);
  return res.data;
}

export async function deleteAgent(agentId: string): Promise<void> {
  await axiosInstance.delete(`/api/agents/${agentId}`);
}

/* -------------------------------------------------------------------------- */
/* Agent Steps                                                                 */
/* -------------------------------------------------------------------------- */

export async function addAgentStep(
  agentId: string,
  step: {
    name: string;
    description?: string;
    type: string;
    config: any;
    order: number;
    nextOnSuccess?: string;
    nextOnFailure?: string;
  }
): Promise<AgentStep> {
  const res = await axiosInstance.post<AgentStep>(
    `/api/agents/${agentId}/steps`,
    step
  );
  return res.data;
}

/* -------------------------------------------------------------------------- */
/* Agent Execution                                                             */
/* -------------------------------------------------------------------------- */

export async function executeAgent(
  agentId: string,
  input: any
): Promise<AgentExecution> {
  const res = await axiosInstance.post<AgentExecution>(
    `/api/agents/${agentId}/execute`,
    { input }
  );
  return res.data;
}

// -----------------------------------------------------------------------------
// Projects – helpers that were missing earlier
// -----------------------------------------------------------------------------

export async function fetchProject(projectId: string): Promise<Project> {
  const res = await axiosInstance.get<Project>(`/api/projects/${projectId}`);
  return res.data;
}

export async function fetchProjectFiles(
  projectId: string
): Promise<ProjectFile[]> {
  const res = await axiosInstance.get<ProjectFile[]>(
    `/api/projects/${projectId}/files`
  );
  return res.data;
}

export async function fetchProjectFile(
  projectId: string,
  fileId: string
): Promise<ProjectFile> {
  const res = await axiosInstance.get<ProjectFile>(
    `/api/projects/${projectId}/files/${fileId}`
  );
  return res.data;
}

export async function createProject(data: {
  name: string;
  description?: string;
}): Promise<Project> {
  const res = await axiosInstance.post<Project>("/api/projects", data);
  return res.data;
}

export async function updateProject(
  projectId: string,
  data: { name?: string; description?: string }
): Promise<Project> {
  const res = await axiosInstance.patch<Project>(
    `/api/projects/${projectId}`,
    data
  );
  return res.data;
}

export async function deleteProject(projectId: string): Promise<void> {
  await axiosInstance.delete(`/api/projects/${projectId}`);
}

/* -------------------------------------------------------------------------- */
/* AI Instruction processing                                                   */
/* -------------------------------------------------------------------------- */

export async function processAgentInstruction(
  projectId: string,
  instruction: string,
  threadId?: string
): Promise<any> {
  const res = await axiosInstance.post(
    `/api/projects/${projectId}/instructions`,
    { instruction, threadId }
  );
  return res.data;
}
