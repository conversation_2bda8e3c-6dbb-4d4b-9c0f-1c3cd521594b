{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/fireworks": "^0.2.14", "@ai-sdk/google": "^1.2.19", "@ai-sdk/groq": "^1.2.9", "@ai-sdk/openai": "^1.3.22", "@clerk/nextjs": "^6.21.0", "@clerk/themes": "^2.2.49", "@codesandbox/sandpack-react": "^2.20.0", "@deepgram/sdk": "^3.11.2", "@hookform/resolvers": "^3.9.0", "@monaco-editor/react": "^4.7.0", "@nanostores/react": "git+https://github.com/ai/react.git", "@next/swc-wasm-nodejs": "13.5.1", "@prisma/client": "^6.4.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@types/node": "20.6.2", "@types/react": "18.2.22", "@types/react-dom": "18.2.7", "@vercel/analytics": "^1.5.0", "ai": "^4.3.16", "ai-actions": "^0.15.1", "autoprefixer": "10.4.15", "axios": "^1.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dedent": "^1.5.3", "dotenv": "^16.4.7", "embla-carousel-react": "^8.3.0", "encoding": "^0.1.13", "eslint": "8.49.0", "eslint-config-next": "13.5.1", "framer-motion": "^12.4.7", "idb": "^8.0.0", "input-otp": "^1.2.4", "katex": "^0.16.21", "lucide-react": "^0.446.0", "monaco-editor": "^0.52.2", "nanoid": "^5.1.3", "next": "13.5.9", "next-themes": "^0.3.0", "postcss": "8.4.30", "prisma": "^6.4.1", "react": "18.2.0", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-hook-form": "^7.53.0", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.1.3", "react-syntax-highlighter": "^15.5.0", "react-textarea-autosize": "^8.5.7", "recharts": "^2.12.7", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-breaks": "^4.0.0", "remark-directive": "^4.0.0", "remark-emoji": "^5.0.1", "remark-frontmatter": "^5.0.0", "remark-gemoji": "^8.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "remark-toc": "^9.0.0", "socket.io-client": "^4.8.1", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "typescript": "5.2.2", "unified": "^11.0.5", "use-debounce": "^10.0.4", "vaul": "^0.9.9", "zod": "^3.24.2"}, "devDependencies": {"@types/katex": "^0.16.7", "@types/react-syntax-highlighter": "^15.5.11", "bufferutil": "^4.0.9", "utf-8-validate": "^6.0.5"}}